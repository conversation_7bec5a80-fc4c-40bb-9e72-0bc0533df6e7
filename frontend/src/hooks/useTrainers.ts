'use client';

import { useState, useEffect, useMemo } from 'react';
import { api } from '@/lib/api';
import { Trainer } from '@/types';

export const useTrainers = () => {
  const [trainers, setTrainers] = useState<Trainer[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteModal, setDeleteModal] = useState({ isOpen: false, trainer: null as Trainer | null });
  const [sortKey, setSortKey] = useState<keyof Trainer>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const fetchTrainers = async () => {
    try {
      setLoading(true);
      const response = await api.trainers.getAll();
      // Pastikan trainers selalu array
      const trainersData = response.data?.trainers;
      setTrainers(Array.isArray(trainersData) ? trainersData : []);
    } catch (error) {
      console.error('Error fetching trainers:', error);
      setTrainers([]); // fallback jika error
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrainers();
  }, []);

  const handleSort = (key: keyof Trainer) => {
    if (sortKey === key) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortKey(key);
      setSortDirection('asc');
    }
  };

  const handleDelete = async () => {
    if (!deleteModal.trainer) return;
    try {
      await api.trainers.delete(deleteModal.trainer.id);
      setTrainers(trainers.filter(t => t.id !== deleteModal.trainer!.id));
      closeDeleteModal();
    } catch (error) {
      console.error('Error deleting trainer:', error);
    }
  };

  const sortedTrainers = useMemo(() => {
    return [...trainers].sort((a, b) => {
      const aValue = a[sortKey];
      const bValue = b[sortKey];
      
      if (aValue == null) return 1;
      if (bValue == null) return -1;

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }, [trainers, sortKey, sortDirection]);

  const openDeleteModal = (trainer: Trainer) => setDeleteModal({ isOpen: true, trainer });
  const closeDeleteModal = () => setDeleteModal({ isOpen: false, trainer: null });

  return {
    trainers,
    loading,
    deleteModal,
    sortKey,
    sortDirection,
    handleSort,
    handleDelete,
    sortedTrainers,
    openDeleteModal,
    closeDeleteModal,
  };
};